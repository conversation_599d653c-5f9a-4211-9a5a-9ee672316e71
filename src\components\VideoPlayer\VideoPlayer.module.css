.videoContainer {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(192, 192, 192, 0.2);
  border: 1px solid rgba(192, 192, 192, 0.1);
}

.videoWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Make sure the video element is displayed properly */
.videoWrapper :global(.video-js) {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
}

/* Make sure the video element is visible */
.videoWrapper :global(.vjs-tech) {
  position: relative !important;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

.customControls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  z-index: 10;
  transition: opacity 0.3s ease;
}

.progressContainer {
  position: relative;
  width: 100%;
  height: 6px;
  margin-bottom: 15px;
  cursor: pointer;
  overflow: visible;
}

.progressBar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  outline: none;
  z-index: 2;
  cursor: pointer;
}

.progressBar::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.progressBar::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.progressBar::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.progressBar::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.progressFill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #c0c0c0, #e0e0e0);
  border-radius: 3px;
  pointer-events: none;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.5);
}

.progressContainer:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.timeDisplay {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.controlsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leftControls,
.rightControls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.controlButton {
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  position: relative;
}

.controlButton:hover {
  background: rgba(224, 224, 224, 0.1);
  color: #e0e0e0;
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

.controlButton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(224, 224, 224, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.controlButton:hover::after {
  opacity: 1;
}

.volumeContainer {
  display: flex;
  align-items: center;
  gap: 5px;
}

.volumeSlider {
  -webkit-appearance: none;
  width: 80px;
  height: 4px;
  border-radius: 2px;
  background: linear-gradient(90deg, rgba(224, 224, 224, 0.8), rgba(192, 192, 192, 0.4));
  outline: none;
  transition: all 0.2s ease;
}

.volumeSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.8);
}

.volumeSlider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.8);
}

.settingsContainer {
  position: relative;
}

.settingsMenu {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 200px;
  background: rgba(16, 16, 16, 0.9);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 0 20px rgba(224, 224, 224, 0.2);
  backdrop-filter: blur(10px);
  z-index: 20;
}

.settingsSection {
  margin-bottom: 15px;
}

.settingsSection h4 {
  color: #e0e0e0;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.speedOptions, .qualityOptions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.speedOption, .qualityOption {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.speedOption:hover, .qualityOption:hover {
  background: rgba(224, 224, 224, 0.1);
  border-color: rgba(224, 224, 224, 0.3);
}

.speedOption.active, .qualityOption.active {
  background: rgba(224, 224, 224, 0.2);
  color: #ffffff;
  position: relative;
}

/* Futuristic overlay text */
.overlayText {
  position: absolute;
  top: 10px;
  left: 10px;
  color: #e0e0e0;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  opacity: 0.8;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.8);
}

/* Futuristic pulse effect on the play button */
@keyframes pulsate {
  0% {
    box-shadow: 0 0 0 0 rgba(224, 224, 224, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(224, 224, 224, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(224, 224, 224, 0);
  }
}

/* Apply this effect to the play button when video is paused */
.controlButton:has(svg[data-icon="play"]) {
  animation: pulsate 2s infinite;
}

/* Holographic hover effect */
.videoContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  background: linear-gradient(45deg, rgba(224, 224, 224, 0.02), rgba(128, 128, 128, 0.05), rgba(224, 224, 224, 0.02));
  background-size: 400% 400%;
  animation: gradientBorder 15s ease infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Scan line effect */
.videoContainer::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 10px;
  border: 1px solid transparent;
  background: linear-gradient(45deg, rgba(224, 224, 224, 0.1), rgba(128, 128, 128, 0.1), rgba(224, 224, 224, 0.1)) border-box;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  pointer-events: none;
  z-index: 1;
  animation: gradientBorder 15s ease infinite;
}

/* Floating particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: rgba(224, 224, 224, 0.7);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0;
  animation: float 6s infinite ease-in-out;
}

@keyframes float {
  0% {
    transform: translateY(100%) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(-100%) translateX(20px);
    opacity: 0;
  }
}

.videoElement {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: contain;
}

.errorMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  text-align: center;
  padding: 20px;
}

.retryButton {
  margin-top: 15px;
  background: #3a86ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.retryButton:hover {
  background: #4361ee;
}

.volumeControl {
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Thumbnail preview on hover */
.thumbnailPreview {
  position: absolute;
  bottom: 15px;
  transform: translateX(-50%);
  background-color: #000;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 4px;
  overflow: hidden;
  width: 160px;
  height: 90px;
  pointer-events: none;
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.3);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 20;
}

.progressContainer:hover .thumbnailPreview {
  opacity: 1;
}

/* Time markers on progress bar */
.timeMarker {
  position: absolute;
  top: -2px;
  width: 4px;
  height: 10px;
  background-color: rgba(224, 224, 224, 0.8);
  border-radius: 2px;
  pointer-events: none;
  z-index: 1;
}

.bufferedProgress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(224, 224, 224, 0.2);
  border-radius: 3px;
  pointer-events: none;
  z-index: 0;
}

/* Keyboard shortcut guide overlay */
.shortcutGuideOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.shortcutGuideContent {
  background: rgba(16, 16, 16, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 0 30px rgba(224, 224, 224, 0.2);
  max-height: 90vh;
  overflow-y: auto;
}

.shortcutGuideContent h3 {
  color: #e0e0e0;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
}

.shortcutGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 15px;
}

.shortcutItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  border: 1px solid rgba(224, 224, 224, 0.1);
}

.shortcutItem:hover {
  background: rgba(224, 224, 224, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(224, 224, 224, 0.2);
}

.shortcutKey {
  background: rgba(224, 224, 224, 0.2);
  border: 1px solid rgba(224, 224, 224, 0.4);
  color: white;
  padding: 4px 8px;
  margin-right: 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: 28px;
  text-align: center;
  display: inline-block;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.shortcutCloseButton {
  display: block;
  margin: 20px auto 0;
  background: rgba(224, 224, 224, 0.2);
  color: #e0e0e0;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 5px;
  padding: 8px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shortcutCloseButton:hover {
  background: rgba(224, 224, 224, 0.4);
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* History menu styles */
.historyMenu {
  position: absolute;
  top: 60px;
  left: 20px;
  background: rgba(16, 16, 16, 0.9);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 15px;
  width: 300px;
  max-height: 400px;
  box-shadow: 0 0 20px rgba(224, 224, 224, 0.2);
  backdrop-filter: blur(10px);
  z-index: 20;
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.historyItem {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(224, 224, 224, 0.1);
  border-radius: 6px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.historyItem:hover {
  background: rgba(224, 224, 224, 0.1);
  border-color: rgba(224, 224, 224, 0.3);
  transform: translateY(-2px);
}

.historyItemName {
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.historyItemMeta {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  display: flex;
  justify-content: space-between;
}

.historyProgress {
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1.5px;
  margin-top: 8px;
  overflow: hidden;
}

.historyProgressFill {
  height: 100%;
  background: linear-gradient(90deg, #e0e0e0, #ffffff);
  border-radius: 1.5px;
}

.emptyHistory {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 15px 0;
  font-style: italic;
  font-size: 14px;
}

/* Fullscreen mode styles */
.fullscreenMode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  z-index: 9999;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
  background-color: #000;
}

.fullscreenMode .videoWrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreenMode .videoElement {
  max-height: 100vh;
  max-width: 100vw;
}

.fullscreenMode .customControls {
  bottom: 0;
  padding: 30px;
  padding-bottom: 40px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0) 150px);
  opacity: 1 !important;
  z-index: 9999;
}

.fullscreenMode:hover .customControls {
  opacity: 1 !important;
}

/* Force controls to be visible in fullscreen */
:fullscreen .customControls,
:-webkit-full-screen .customControls,
:-moz-full-screen .customControls,
:-ms-fullscreen .customControls {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Make controls more visible in fullscreen */
.fullscreenMode .controlButton {
  width: 48px;
  height: 48px;
  font-size: 22px;
}

.fullscreenMode .progressContainer {
  height: 8px;
  margin-bottom: 20px;
}

.fullscreenMode .timeDisplay {
  font-size: 16px;
  margin-bottom: 15px;
}

/* Ensure controls are visible on touch devices */
@media (pointer: coarse) {
  .customControls {
    opacity: 1 !important;
  }
  
  .fullscreenMode .customControls {
    opacity: 1 !important;
  }
}

/* Force controls to always be visible in fullscreen */
.fullscreenMode .customControls {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  flex-direction: column !important;
  pointer-events: auto !important;
  transform: none !important;
}

/* Always show controls in fullscreen */
.fullscreenMode .customControls {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  flex-direction: column !important;
  z-index: 9999;
  pointer-events: auto !important;
  transform: none !important;
}

/* Increase z-index for fullscreen controls */
.fullscreenMode .controlsRow,
.fullscreenMode .progressContainer,
.fullscreenMode .timeDisplay {
  z-index: 10000;
  pointer-events: auto !important;
}

/* Make sure controls are always visible on mobile devices */
@media (max-width: 768px) {
  .customControls {
    opacity: 1 !important;
    visibility: visible !important;
  }
}

.qualityIndicator {
  color: #e0e0e0;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(224, 224, 224, 0.8);
  margin-top: 4px;
  font-size: 12px;
  letter-spacing: 1px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
    box-shadow: 0 0 15px rgba(224, 224, 224, 0.8);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.qualityNotification {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #e0e0e0;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  text-align: center;
  z-index: 100;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(224, 224, 224, 0.5);
  box-shadow: 0 0 20px rgba(224, 224, 224, 0.3);
  min-width: 180px;
}

.qualityNotification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(224, 224, 224, 0.1), transparent);
  border-radius: 20px;
  z-index: -1;
  animation: gradientBorder 2s ease infinite;
}

/* Screenshot Preview */
.screenshotPreview {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(16, 20, 34, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 8px;
  padding: 15px;
  width: 80%;
  max-width: 800px;
  max-height: 80%;
  box-shadow: 0 0 30px rgba(224, 224, 224, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.screenshotHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.screenshotHeader h3 {
  color: #e0e0e0;
  font-size: 18px;
  margin: 0;
  text-shadow: 0 0 10px rgba(224, 224, 224, 0.5);
}

.screenshotDownload,
.screenshotClose {
  background: rgba(224, 224, 224, 0.2);
  color: #e0e0e0;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.screenshotDownload:hover,
.screenshotClose:hover {
  background: rgba(224, 224, 224, 0.4);
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

.screenshotClose {
  font-size: 20px;
  padding: 0 10px;
}

.screenshotImageContainer {
  overflow: auto;
  max-height: 60vh;
}

.screenshotImage {
  width: 100%;
  height: auto;
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 4px;
}

/* Bookmark Input */
.bookmarkInputContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(16, 20, 34, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  box-shadow: 0 0 30px rgba(224, 224, 224, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.bookmarkInputContainer h3 {
  color: #e0e0e0;
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
  text-shadow: 0 0 10px rgba(224, 224, 224, 0.5);
}

.bookmarkInput {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 4px;
  padding: 10px;
  color: white;
  font-size: 14px;
  margin-bottom: 15px;
}

.bookmarkInput:focus {
  outline: none;
  border-color: rgba(224, 224, 224, 0.7);
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.3);
}

.bookmarkButtons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.bookmarkSave,
.bookmarkCancel {
  flex: 1;
  background: rgba(224, 224, 224, 0.2);
  color: white;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 4px;
  padding: 8px 0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bookmarkSave {
  background: rgba(224, 224, 224, 0.3);
  color: #e0e0e0;
}

.bookmarkSave:hover,
.bookmarkCancel:hover {
  background: rgba(224, 224, 224, 0.4);
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

/* Bookmarks List */
.bookmarksContainer {
  position: absolute;
  top: 60px;
  right: 20px;
  background: rgba(16, 20, 34, 0.8);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 15px;
  width: 200px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 0 20px rgba(224, 224, 224, 0.2);
  backdrop-filter: blur(10px);
  z-index: 90;
}

.bookmarksTitle {
  color: #e0e0e0;
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
}

.bookmarksList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bookmarkItem {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(224, 224, 224, 0.1);
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bookmarkItem:hover {
  background: rgba(224, 224, 224, 0.1);
  border-color: rgba(224, 224, 224, 0.3);
}

.bookmarkContent {
  flex: 1;
  overflow: hidden;
}

.bookmarkTime {
  display: block;
  color: #e0e0e0;
  font-size: 12px;
  margin-bottom: 3px;
}

.bookmarkLabel {
  display: block;
  color: white;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmarkDelete {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(128, 128, 128, 0.2);
  color: #e0e0e0;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.bookmarkDelete:hover {
  background: rgba(224, 224, 224, 0.4);
}

/* Statistics Overlay */
.statisticsOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.statisticsContent {
  background: rgba(16, 16, 16, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 0 30px rgba(224, 224, 224, 0.2);
  max-height: 90vh;
  overflow-y: auto;
}

.statisticsContent h3 {
  color: #e0e0e0;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
}

.statisticsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.statisticsItem {
  background: rgba(30, 30, 30, 0.6);
  border: 1px solid rgba(224, 224, 224, 0.2);
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
}

.statisticsItem:hover {
  background: rgba(40, 40, 40, 0.8);
  border-color: rgba(224, 224, 224, 0.4);
}

.statisticsLabel {
  color: #a0a0a0;
  font-size: 12px;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.statisticsValue {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
}

.statisticsClose {
  background: rgba(224, 224, 224, 0.2);
  color: #e0e0e0;
  border: 1px solid rgba(224, 224, 224, 0.4);
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 20px;
  display: block;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.2s ease;
}

.statisticsClose:hover {
  background: rgba(224, 224, 224, 0.4);
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.3);
}

/* Enhanced visual effects */
.videoContainer.enhancedEffects::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(224, 224, 224, 0.2) 30%,
    rgba(224, 224, 224, 0.4) 45%,
    rgba(128, 128, 128, 0.4) 55%,
    rgba(128, 128, 128, 0.2) 70%,
    transparent 100%
  );
  z-index: 1;
  background-size: 400% 400%;
  animation: gradientBorder 8s ease infinite;
  pointer-events: none;
  border-radius: 10px;
  filter: blur(3px);
}

/* Active control button */
.controlButton.active {
  background: rgba(224, 224, 224, 0.3);
  color: #e0e0e0;
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.6);
}

/* Animations */
@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
    border-color: rgba(224, 224, 224, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(224, 224, 224, 0.8);
    border-color: rgba(224, 224, 224, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
    border-color: rgba(224, 224, 224, 0.3);
  }
}

/* Visual effects controls */
.effectsOptions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.effectsSliderLabel {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 14px;
}

.effectsSlider {
  flex: 1;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
}

.effectsSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.7);
}

.effectsSlider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.7);
  border: none;
}

.effectsSlider:disabled {
  opacity: 0.5;
}

.effectsToggle {
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: rgba(128, 128, 128, 0.3);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
  border: 1px solid rgba(224, 224, 224, 0.3);
}

.effectsToggle.active {
  background-color: rgba(224, 224, 224, 0.3);
  border-color: rgba(224, 224, 224, 0.6);
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.4) inset;
}

/* Enhanced visual effects based on intensity */
.videoContainer.enhancedEffects::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 10px;
  border: 1px solid transparent;
  background: linear-gradient(45deg, rgba(224, 224, 224, 0.2), rgba(192, 192, 192, 0.1), rgba(224, 224, 224, 0.2)) border-box;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  pointer-events: none;
  z-index: 1;
  animation: gradientBorder 15s ease infinite;
}

/* Bookmark Preview */
.bookmarkPreview {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 90px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.bookmarkPreview::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(224, 224, 224, 0.5);
}

.bookmarkPreviewVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bookmarkPreviewTime {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: #e0e0e0;
  font-size: 12px;
  padding: 2px 6px;
  text-align: center;
}

/* Chapters */
.chapterIndicator {
  margin-left: 15px;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  background: rgba(224, 224, 224, 0.1);
  padding: 3px 8px;
  border-radius: 4px;
  border: 1px solid rgba(224, 224, 224, 0.3);
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  display: inline-block;
  vertical-align: middle;
}

.chapterIndicator:hover {
  background: rgba(224, 224, 224, 0.2);
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.3);
}

.chaptersMenu {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(16, 20, 34, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 8px;
  padding: 15px;
  width: 300px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.chaptersMenu h4 {
  color: #e0e0e0;
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
}

.chaptersList {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chapterItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chapterItem:hover {
  background: rgba(224, 224, 224, 0.1);
}

.activeChapter {
  background: rgba(224, 224, 224, 0.15);
  border-left: 3px solid #e0e0e0;
}

.chapterTime {
  color: #e0e0e0;
  font-size: 12px;
  min-width: 45px;
}

.chapterTitle {
  color: white;
  font-size: 14px;
  flex: 1;
}

.chapterMarker {
  position: absolute;
  top: 0;
  width: 4px;
  height: 100%;
  background: rgba(224, 224, 224, 0.7);
  cursor: pointer;
  z-index: 2;
  transition: all 0.2s ease;
}

.chapterMarker::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 10px;
  background: #e0e0e0;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(224, 224, 224, 0.8);
}

.chapterMarker:hover {
  background: rgba(224, 224, 224, 1);
}

.chapterMarker:hover::before {
  animation: pulse 1.5s infinite;
}

.qualityOptions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.qualityOption {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 4px;
  padding: 6px 0;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.qualityOption::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(224, 224, 224, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.qualityOption:hover::before {
  transform: translateX(100%);
}

.qualityOption.active {
  background: rgba(224, 224, 224, 0.3);
  color: #e0e0e0;
  border-color: rgba(224, 224, 224, 0.6);
  box-shadow: 0 0 10px rgba(224, 224, 224, 0.4);
}

/* Premium quality indicators */
.qualityOption[data-quality="8K"]::after,
.qualityOption[data-quality="4K"]::after,
.qualityOption[data-quality="2K"]::after {
  content: '✦';
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 10px;
  color: #e0e0e0;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.8);
}

.premiumQualityIndicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #e0e0e0;
  box-shadow: 0 0 6px rgba(224, 224, 224, 0.8);
  margin-right: 5px;
  animation: pulse 2s infinite;
}

.qualityNotificationIcon {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  text-shadow: 0 0 10px rgba(224, 224, 224, 0.8);
}

.qualityNotificationInfo {
  font-size: 12px;
  margin-top: 5px;
  opacity: 0.8;
}

.qualityWarning {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 10px;
  font-size: 12px;
  color: #e0e0e0;
  background: rgba(224, 224, 224, 0.1);
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid rgba(224, 224, 224, 0.3);
}

.networkQualityIndicator {
  margin-left: 8px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.networkHigh {
  color: #e0e0e0;
  text-shadow: 0 0 8px rgba(224, 224, 224, 0.7);
}

.networkMedium {
  color: #c0c0c0;
  text-shadow: 0 0 8px rgba(192, 192, 192, 0.7);
}

.networkLow {
  color: #a0a0a0;
  text-shadow: 0 0 8px rgba(160, 160, 160, 0.7);
}

.comparisonButton {
  width: 100%;
  background: linear-gradient(to right, rgba(224, 224, 224, 0.2), rgba(128, 128, 128, 0.2));
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 4px;
  padding: 8px 0;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 15px;
}

.comparisonButton:hover {
  background: linear-gradient(to right, rgba(224, 224, 224, 0.3), rgba(128, 128, 128, 0.3));
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

/* Quality Comparison */
.qualityComparisonOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 10, 20, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.qualityComparisonContent {
  background: rgba(16, 20, 34, 0.95);
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 8px;
  padding: 25px;
  width: 90%;
  max-width: 900px;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 0 30px rgba(224, 224, 224, 0.3);
}

.qualityComparisonContent h3 {
  color: #e0e0e0;
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 0 10px rgba(224, 224, 224, 0.5);
}

.qualityComparisonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.qualityComparisonItem {
  border-radius: 8px;
  padding: 15px;
  background: rgba(30, 30, 30, 0.8);
  border: 1px solid rgba(224, 224, 224, 0.2);
  transition: all 0.2s ease;
}

.qualityComparisonItem:hover {
  background: rgba(40, 40, 40, 0.9);
  box-shadow: 0 0 20px rgba(224, 224, 224, 0.2);
  border-color: rgba(224, 224, 224, 0.4);
  transform: translateY(-2px);
}

.qualityComparisonTitle {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.5);
}

.qualityComparisonImage {
  width: 100%;
  height: 140px;
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
}

.qualityComparisonImagePlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.qualityComparisonSpecs {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  line-height: 1.6;
}

.qualityComparisonClose {
  display: block;
  margin: 0 auto;
  background: rgba(224, 224, 224, 0.2);
  color: #e0e0e0;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 5px;
  padding: 8px 25px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.qualityComparisonClose:hover {
  background: rgba(224, 224, 224, 0.4);
  box-shadow: 0 0 15px rgba(224, 224, 224, 0.4);
}

.quality8K {
  color: #e0e0e0;
  text-shadow: 0 0 5px rgba(224, 224, 224, 0.8);
}

.quality4K {
  color: #d0d0d0;
  text-shadow: 0 0 4px rgba(208, 208, 208, 0.7);
}

.quality2K {
  color: #b0b0b0;
  text-shadow: 0 0 3px rgba(176, 176, 176, 0.6);
}

.qualityStandard {
  color: #909090;
}

.videoPlayerContainer {
  position: relative;
  width: 100%;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

.videoContainer {
  width: 100%;
  height: 100%;
}

.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* Futuristic UI elements */
.futuristicPlayer :global(.vjs-control-bar) {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  height: 4em;
  transition: all 0.3s ease;
}

.futuristicPlayer :global(.vjs-button) {
  color: #fff;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.futuristicPlayer :global(.vjs-button:hover) {
  color: #fff;
  opacity: 1;
  transform: scale(1.1);
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.futuristicPlayer :global(.vjs-progress-control) {
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  height: 10px;
  transform: translateY(0);
  transition: transform 0.2s ease;
}

.futuristicPlayer:hover :global(.vjs-progress-control) {
  transform: translateY(10px);
}

.futuristicPlayer :global(.vjs-progress-holder) {
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  transition: height 0.2s ease;
}

.futuristicPlayer:hover :global(.vjs-progress-holder) {
  height: 6px;
}

.futuristicPlayer :global(.vjs-play-progress) {
  background: linear-gradient(to right, rgba(192, 192, 192, 0.7), #fff);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.futuristicPlayer :global(.vjs-play-progress::before) {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  top: -3px;
}

.futuristicPlayer :global(.vjs-big-play-button) {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 3em;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.futuristicPlayer :global(.vjs-big-play-button:hover) {
  background: rgba(0, 0, 0, 0.8);
  border-color: #fff;
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

.futuristicPlayer :global(.vjs-volume-panel) {
  transition: all 0.3s ease;
}

.futuristicPlayer :global(.vjs-volume-control) {
  max-width: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.futuristicPlayer :global(.vjs-volume-panel:hover .vjs-volume-control) {
  max-width: 8em;
  opacity: 1;
}

.futuristicPlayer :global(.vjs-volume-bar) {
  background: rgba(255, 255, 255, 0.2);
  height: 3px;
}

.futuristicPlayer :global(.vjs-volume-level) {
  background: linear-gradient(to right, rgba(192, 192, 192, 0.7), #fff);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.futuristicPlayer :global(.vjs-time-control) {
  font-family: monospace;
  font-size: 0.9em;
  opacity: 0.8;
}

/* Futuristic overlay effects */
.scanLine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0.3;
  z-index: 2;
  pointer-events: none;
  animation: scanAnimation 8s linear infinite;
}

@keyframes scanAnimation {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(1000%);
  }
}

.glitchEffect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  opacity: 0;
  z-index: 2;
  pointer-events: none;
}

.videoPlayerContainer:hover .glitchEffect {
  animation: glitchAnimation 0.5s ease-out;
}

@keyframes glitchAnimation {
  0% {
    opacity: 0;
  }
  5% {
    opacity: 0.3;
    transform: translate(5px, 0);
    background: rgba(255, 255, 255, 0.1);
  }
  10% {
    opacity: 0;
  }
  15% {
    opacity: 0.3;
    transform: translate(-5px, 0);
    background: rgba(255, 255, 255, 0.1);
  }
  20% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

.cornerAccents {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.topLeft {
  top: 10px;
  left: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.7);
  border-left: 1px solid rgba(255, 255, 255, 0.7);
}

.topRight {
  top: 10px;
  right: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.7);
  border-right: 1px solid rgba(255, 255, 255, 0.7);
}

.bottomLeft {
  bottom: 10px;
  left: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.7);
  border-left: 1px solid rgba(255, 255, 255, 0.7);
}

.bottomRight {
  bottom: 10px;
  right: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.7);
  border-right: 1px solid rgba(255, 255, 255, 0.7);
}

.techBorder {
  position: absolute;
  inset: 0;
  border: 1px solid transparent;
  border-image: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3), transparent) 1;
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.videoPlayerContainer:hover .techBorder {
  opacity: 1;
}

/* Interactive focus elements */
.focusRing {
  position: absolute;
  inset: -5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  z-index: 1;
  pointer-events: none;
  animation: pulseRing 2s ease-out infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.dataOverlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 2;
}

.dataPoint {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  animation: blinkPoint 2s infinite;
}

.dataPoint:nth-child(1) {
  top: 20px;
  right: 20px;
  animation-delay: 0s;
}

.dataPoint:nth-child(2) {
  bottom: 20px;
  left: 20px;
  animation-delay: 0.5s;
}

.dataPoint:nth-child(3) {
  bottom: 20px;
  right: 20px;
  animation-delay: 1s;
}

@keyframes blinkPoint {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.3;
  }
}

.dataLine {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 30px;
  height: 1px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.7), transparent);
  transform-origin: left center;
  animation: rotateLine 4s linear infinite;
}

@keyframes rotateLine {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Status indicator */
.statusIndicator {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 10px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 10;
  pointer-events: none;
}

.statusIndicator.visible {
  opacity: 1;
  transform: translateY(0);
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #fff;
  animation: pulseDot 1.5s infinite;
}

@keyframes pulseDot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.statusText {
  font-family: monospace;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
}

/* Control visibility */
.hideControls :global(.vjs-control-bar) {
  opacity: 0;
  transform: translateY(100%);
}

.hideControls :global(.vjs-big-play-button) {
  display: none;
}

/* Loading overlay */
.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.loadingSpinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinnerRing {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #00f7ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinnerRing:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #0055dd;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinnerRing:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #ffffff;
  animation-duration: 2s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  color: #00f7ff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Error overlay */
.errorOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.errorContent {
  text-align: center;
  padding: 40px;
  background: rgba(20, 20, 20, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 0, 0, 0.3);
  box-shadow: 0 0 30px rgba(255, 0, 0, 0.2);
  max-width: 400px;
}

.errorIcon {
  font-size: 48px;
  margin-bottom: 16px;
  filter: drop-shadow(0 0 10px rgba(255, 0, 0, 0.5));
}

.errorTitle {
  color: #ff4444;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.errorMessage {
  color: #cccccc;
  font-size: 16px;
  margin-bottom: 24px;
  line-height: 1.5;
}

.retryButton {
  background: linear-gradient(45deg, #ff4444, #cc0000);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.retryButton:hover {
  background: linear-gradient(45deg, #ff6666, #ff4444);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

.retryButton:active {
  transform: translateY(0);
}

/* Video Info Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.videoInfoModal {
  background: rgba(20, 20, 20, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.videoInfoHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.videoInfoHeader h3 {
  color: #00f7ff;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.videoInfoClose {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.videoInfoClose:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff4444;
}

.videoInfoContent {
  padding: 20px;
}

.videoInfoSection {
  margin-bottom: 24px;
}

.videoInfoSection:last-child {
  margin-bottom: 0;
}

.videoInfoSection h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

.videoInfoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.videoInfoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.videoInfoLabel {
  color: #ccc;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.videoInfoValue {
  color: #00f7ff;
  font-size: 14px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

@media (max-width: 768px) {
  .videoInfoGrid {
    grid-template-columns: 1fr;
  }

  .videoInfoModal {
    width: 95%;
    margin: 20px;
  }
}