{"name": "futuristic-player", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/core": "^8.1.2", "@mantine/hooks": "^8.1.2", "@mantine/notifications": "^8.1.2", "@vercel/analytics": "^1.5.0", "@videojs/http-streaming": "^3.17.0", "dashjs": "^5.0.3", "framer-motion": "^12.23.0", "hls.js": "^1.6.6", "next": "15.3.5", "plyr": "^3.7.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "shikwasa": "^2.2.1", "video.js": "^8.23.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}