# Futuristic Video Player

A hyper-futuristic, feature-rich video player built with Next.js that aims to surpass existing video players like VLC with advanced features and a modern UI.

![Futuristic Video Player Preview](preview.png)

## Features

- **Cinematic UI**: Immersive, futuristic interface with motion animations and holographic effects
- **Advanced Controls**: Custom progress bar, volume controls, playback speed settings, and more
- **Format Support**: Play MP4, HLS, DASH, and many other video formats seamlessly
- **Responsive Design**: Adapts perfectly to any device or screen size
- **Keyboard Shortcuts**: Control playback with intuitive keyboard shortcuts
- **High Performance**: Optimized for smooth playback and low resource usage
- **Playlist Support**: Create and manage playlists with thumbnails and metadata
- **Adaptive Streaming**: HLS and DASH support for adaptive bitrate streaming
- **Picture-in-Picture**: Continue watching while browsing other content
- **AI Enhancements**: Smart upscaling, noise reduction and color grading

## Technologies Used

- **Next.js 14**: React framework with App Router
- **TypeScript**: For type safety and better developer experience
- **Video.js**: Core video playback engine
- **HLS.js & DASH.js**: For streaming capabilities
- **Framer Motion**: For smooth animations and transitions
- **TailwindCSS**: For styling and responsive design
- **React Icons**: For beautiful iconography

## Getting Started

### Prerequisites

- Node.js (v18 or newer)
- npm or yarn

### Installation

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/futuristic-player.git
   cd futuristic-player
   ```

2. Install dependencies
   ```bash
   npm install
   # or
   yarn
   ```

3. Run the development server
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result

## Usage

The player can be easily integrated into any Next.js or React application:

```tsx
import VideoPlayer from '@/components/VideoPlayer';

export default function MyPage() {
  return (
    <div className="my-container">
      <VideoPlayer 
        src="https://example.com/video.mp4" 
        poster="https://example.com/poster.jpg"
        fluid={true}
      />
    </div>
  );
}
```

## API Reference

### VideoPlayer Props

| Prop       | Type      | Default      | Description                               |
|------------|-----------|--------------|-------------------------------------------|
| src        | string    | required     | URL of the video source                   |
| poster     | string    | undefined    | URL of the video poster image             |
| type       | string    | 'video/mp4'  | MIME type of the video                    |
| autoPlay   | boolean   | false        | Whether to auto-play the video            |
| controls   | boolean   | true         | Show default controls                     |
| fluid      | boolean   | true         | Make the player responsive                |
| width      | number    | 640          | Width of the player in px                 |
| height     | number    | 360          | Height of the player in px                |
| options    | object    | {}           | Additional Video.js options               |

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Video.js](https://videojs.com/)
- [Next.js](https://nextjs.org/)
- [Framer Motion](https://www.framer.com/motion/)
- [TailwindCSS](https://tailwindcss.com/)
